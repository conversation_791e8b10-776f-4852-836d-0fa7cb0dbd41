{% extends 'base.html' %}

{% block title %}Delete Admission Test - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-red-800">Delete Admission Test Question</h2>
            <p class="text-gray-600 mt-1">This action cannot be undone</p>
        </div>
        <a href="{% url 'management_admission_tests' %}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to List
        </a>
    </div>

    <!-- Warning Card -->
    <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-medium text-red-800">Warning: Permanent Deletion</h3>
                <div class="mt-2 text-sm text-red-700">
                    <p>You are about to permanently delete this admission test question. This action will:</p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>Remove the question from all future admission tests</li>
                        <li>Delete all associated student answer data ({{ attempts_count }} attempts)</li>
                        <li>Cannot be undone or recovered</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Question Details -->
    <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Question to be Deleted</h3>
        
        <!-- Status and Metadata -->
        <div class="flex items-center space-x-3 mb-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                {% if test.subject_area == 'math' %}bg-blue-100 text-blue-800
                {% elif test.subject_area == 'science' %}bg-green-100 text-green-800
                {% elif test.subject_area == 'english' %}bg-purple-100 text-purple-800
                {% elif test.subject_area == 'history' %}bg-amber-100 text-amber-800
                {% else %}bg-gray-100 text-gray-800{% endif %}">
                {{ test.get_subject_area_display }}
            </span>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                {% if test.difficulty_level == 'easy' %}bg-green-100 text-green-800
                {% elif test.difficulty_level == 'medium' %}bg-yellow-100 text-yellow-800
                {% else %}bg-red-100 text-red-800{% endif %}">
                {{ test.get_difficulty_level_display }}
            </span>
            {% if test.is_active %}
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Active
            </span>
            {% else %}
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Inactive
            </span>
            {% endif %}
        </div>
        
        <!-- Question Text -->
        <div class="mb-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Question:</h4>
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <p class="text-gray-900">{{ test.question_text }}</p>
            </div>
        </div>
        
        <!-- Answer Options -->
        <div class="mb-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Answer Options:</h4>
            <div class="space-y-2">
                {% for option in test.answer_options %}
                <div class="flex items-center p-3 rounded-lg border 
                    {% if option == test.correct_answer %}border-green-300 bg-green-50{% else %}border-gray-200 bg-white{% endif %}">
                    <span class="flex-shrink-0 w-6 h-6 bg-gray-200 text-gray-700 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                        {{ forloop.counter }}
                    </span>
                    <span class="text-gray-900">{{ option }}</span>
                    {% if option == test.correct_answer %}
                    <svg class="w-5 h-5 text-green-600 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Usage Statistics -->
        {% if attempts_count > 0 %}
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div class="flex items-center">
                <svg class="w-5 h-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <p class="text-sm font-medium text-amber-800">Impact Warning</p>
                    <p class="text-sm text-amber-700">This question has been attempted {{ attempts_count }} time{{ attempts_count|pluralize }} by students. Deleting it will remove all this data.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Confirmation Form -->
    <div class="bg-white rounded-lg border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
        
        <form method="post" class="space-y-4">
            {% csrf_token %}
            
            <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <label class="flex items-start">
                    <input type="checkbox" required 
                           class="mt-1 h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                    <span class="ml-3 text-sm text-gray-700">
                        I understand that this action is permanent and cannot be undone. I want to delete this admission test question and all associated data.
                    </span>
                </label>
            </div>
            
            <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                <a href="{% url 'management_admission_tests' %}" 
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition-colors">
                    Cancel
                </a>
                <a href="{% url 'admin_admission_test_view' test.id %}" 
                   class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                    View Details Instead
                </a>
                <button type="submit" 
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-transform transform hover:scale-105">
                    Delete Question
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
