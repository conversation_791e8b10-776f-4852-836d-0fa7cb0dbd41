<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Course Recommendation System{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10/dist/htmx.min.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Three.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }
        
        .htmx-indicator {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .htmx-request .htmx-indicator {
            opacity: 1;
        }
        
        
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .three-canvas {
            border-radius: 0.5rem;
        }
        
        /* Enhanced sidebar styles */
        .sidebar-nav-item {
            position: relative;
            overflow: hidden;
        }
        
        .sidebar-nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .sidebar-nav-item:hover::before {
            left: 100%;
        }
        
        /* Unified active link style for Unpoly */
        .sidebar-nav-item.up-current {
            background: linear-gradient(135deg, rgb(239 246 255) 0%, rgb(219 234 254) 100%);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            color: #1d4ed8; /* text-primary-700 */
        }
        
        .sidebar-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .sidebar-nav-item:hover .sidebar-icon {
            transform: scale(1.1) rotate(5deg);
        }
        
        /* Smooth transitions for all interactive elements */
        * {
            transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
            transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
            transition-duration: 150ms;
        }
        
        /* Mobile responsive improvements */
        @media (max-width: 1024px) {
            .sidebar-nav-item {
                padding: 0.75rem 1rem;
                font-size: 0.875rem;
            }
            
            .sidebar-nav-item svg {
                width: 1.25rem;
                height: 1.25rem;
            }
        }
        
        @media (max-width: 768px) {
            .sidebar-nav-item {
                padding: 1rem;
                font-size: 1rem;
            }
            
            .sidebar-nav-item svg {
                width: 1.5rem;
                height: 1.5rem;
            }
        }
        
        /* Ensure sidebar is properly positioned on mobile */
        @media (max-width: 1024px) {
            aside {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
            }
        }
        
        /* Sidebar polish */
        aside {
            padding-top: 0;
            background: linear-gradient(to bottom, #fff 0%, #f8fafc 100%);
        }
        .sidebar-nav-item {
            margin-bottom: 0.25rem;
            padding-left: 1.25rem;
            padding-right: 1.25rem;
            border-left: 4px solid transparent;
            transition: background 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
            font-weight: 500;
            display: flex;
            align-items: center;
            min-height: 44px;
        }
        .sidebar-nav-item.up-current {
            border-left: 4px solid #2563eb;
            box-shadow: 0 2px 8px 0 rgba(59,130,246,0.07);
        }
        .sidebar-icon {
            margin-right: 0.75rem;
            width: 1.25rem;
            height: 1.25rem;
            transition: color 0.2s, transform 0.2s;
        }
        .sidebar-nav-item.up-current .sidebar-icon {
            color: #2563eb; /* text-primary-600 */
            transform: scale(1.1);
        }
        .sidebar-nav-item:hover .sidebar-icon {
            color: #2563eb;
        }
        .sidebar-footer {
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
            padding: 1rem 1.25rem;
            margin-top: auto;
        }
        .sidebar-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        .sidebar-avatar {
            width: 2rem;
            height: 2rem;
            border-radius: 9999px;
            background: #dbeafe;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #2563eb;
            font-size: 1.1rem;
        }
        .sidebar-user-info {
            min-width: 0;
        }
        .sidebar-user-info p {
            margin: 0;
            font-size: 0.95rem;
            font-weight: 600;
            color: #22223b;
            line-height: 1.1;
        }
        .sidebar-user-info span {
            font-size: 0.8rem;
            color: #64748b;
        }
        @media (max-width: 1024px) {
            aside {
                position: fixed;
                top: 0;
                left: 0;
                height: 100vh;
                z-index: 50;
                width: 16rem;
                box-shadow: 2px 0 8px 0 rgba(0,0,0,0.07);
            }
            .sidebar-close-btn {
                display: flex;
                justify-content: flex-end;
                padding: 1rem 1.25rem 0.5rem 1.25rem;
            }
        }
        @media (max-width: 640px) {
            .sidebar-nav-item {
                font-size: 1rem;
                padding-left: 1rem;
                padding-right: 1rem;
            }
            .sidebar-footer {
                padding: 1rem;
            }
        }
    </style>
    
    {% load static %}
        <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen" x-data="{ sidebarOpen: false }">
    <div class="flex min-h-screen">
        <!-- Sidebar (overlaps nav header on mobile, static on desktop) -->
        <aside class="fixed inset-y-0 left-0 z-40 w-64 bg-gradient-to-b from-white to-gray-50 shadow-lg border-r border-gray-200 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-auto" 
               :class="{ 'translate-x-0': sidebarOpen, '-translate-x-full': !sidebarOpen }">
            {% block sidebar %}{% endblock %}
        </aside>

        <!-- Main wrapper (shifted right on desktop) -->
        <div class="flex-1 flex flex-col">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30" style="height:72px;min-height:72px;">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
                <div class="flex justify-between items-center h-16" style="height:72px;min-height:72px;">
                    <!-- Sidebar toggle button (mobile only) -->
                    <button @click="sidebarOpen = !sidebarOpen" class="text-gray-600 hover:text-primary-600 p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 mr-2 lg:hidden">
                        <svg x-show="!sidebarOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                        <svg x-show="sidebarOpen" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                    <div class="flex-1"></div>
                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                            <span class="text-sm text-gray-600 font-medium">{{ user.get_full_name|default:user.username }}</span>
                            <form method="post" action="{% url 'logout' %}" up-target="_top">
                                {% csrf_token %}
                                <button type="submit" class="text-sm text-gray-600 hover:text-primary-600 font-medium transition-colors bg-transparent border-0 p-0">Logout</button>
                            </form>
                        {% else %}
                            <a href="{% url 'login' %}" class="text-sm text-gray-600 hover:text-primary-600 font-medium transition-colors">Login</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </nav>
        <!-- Main content -->
        <main class="flex-1 {% block main_classes %}p-4 sm:p-6{% endblock %}">
            <!-- Loading indicator -->
            <div class="htmx-indicator fixed top-4 right-4 z-50">
                <div class="bg-primary-600 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading...
                </div>
            </div>
            <!-- Flash messages -->
            {% if messages %}
                <div class="mb-6">
                    {% for message in messages %}
                        <div class="rounded-md p-4 mb-4 {{ message.tags|default:'info' }}"
                             x-data="{ show: true }"
                             x-show="show"
                             x-transition:enter="transition ease-out duration-300"
                             x-transition:enter-start="opacity-0 transform scale-90"
                             x-transition:enter-end="opacity-100 transform scale-100"
                             x-transition:leave="transition ease-in duration-300"
                             x-transition:leave-start="opacity-100 transform scale-100"
                             x-transition:leave-end="opacity-0 transform scale-90">
                            <div class="flex justify-between items-center">
                                <div class="flex">
                                    <div class="ml-3">
                                        <p class="text-sm font-medium">{{ message }}</p>
                                    </div>
                                </div>
                                <button @click="show = false" class="ml-4 text-gray-400 hover:text-gray-600">
                                    <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
            {% block content %}{% endblock %}
        </main>
    </div>
</div>
    <!-- Scroll to top button -->
    <div x-data="{ show: false, init() { window.addEventListener('scroll', () => { this.show = window.scrollY > 300; }); } }" x-show="show" x-transition.opacity.duration.300ms class="fixed bottom-4 right-4 z-50">
        <button @click="window.scrollTo({ top: 0, behavior: 'smooth' })" class="bg-primary-600 hover:bg-primary-700 text-white rounded-full p-3 shadow-lg transition-transform transform hover:scale-110">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
            </svg>
        </button>
    </div>
    <!-- HTMX Configuration -->
    <script>
        // HTMX event listeners
        document.body.addEventListener('htmx:configRequest', (event) => {
            event.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });
        // HTMX success/error handling
        document.body.addEventListener('htmx:afterRequest', (event) => {
            if (event.detail.xhr.status >= 400) {
                console.error('HTMX request failed:', event.detail.xhr.responseText);
            }
        });
    </script>
    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
