from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.test import Client
from course_matcher.models import StudentProfile, AcademicRecord, AdmissionTestAttempt, StudentSurvey, Course, Department
from course_matcher.views import get_profile_completion_status


class Command(BaseCommand):
    help = 'Test profile completion synchronization with recommendations display'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing profile completion synchronization...'))
        
        # Create test client
        client = Client()
        
        # Create or get a test user
        user, created = User.objects.get_or_create(
            username='test_sync_student',
            defaults={
                'first_name': 'Test',
                'last_name': 'SyncStudent',
                'email': '<EMAIL>'
            }
        )
        user.set_password('testpass123')
        user.save()
        
        # Create student profile WITHOUT major (incomplete)
        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={
                'student_id': 'TESTSYNC001',
                'year': 'sophomore',
                'interests': [],
                'career_goals': '',
                'major': None,  # Explicitly set to None
            }
        )
        
        self.stdout.write(f'Testing with student: {student.user.first_name} {student.user.last_name} (ID: {student.student_id})')
        
        # Test 1: Completely empty profile
        self.stdout.write('\n--- Test 1: Empty Profile (0/6 complete) ---')
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Login and test recommendations access
        client.login(username='test_sync_student', password='testpass123')
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 2: Add interests only (1/6 complete)
        self.stdout.write('\n--- Test 2: Add Interests Only (1/6 complete) ---')
        student.interests = ['Machine Learning', 'Web Development']
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 3: Add career goals (2/6 complete)
        self.stdout.write('\n--- Test 3: Add Career Goals (2/6 complete) ---')
        student.career_goals = 'I want to become a software engineer specializing in AI.'
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 4: Add academic record (3/6 complete)
        self.stdout.write('\n--- Test 4: Add Academic Record (3/6 complete) ---')
        dept, _ = Department.objects.get_or_create(
            code='CS',
            defaults={'name': 'Computer Science'}
        )
        course, _ = Course.objects.get_or_create(
            code='CS101',
            defaults={
                'name': 'Introduction to Programming',
                'description': 'Basic programming concepts',
                'credits': 3,
                'department': dept,
                'difficulty': 'beginner'
            }
        )
        record, _ = AcademicRecord.objects.get_or_create(
            student=student,
            course=course,
            defaults={
                'semester': 'fall',
                'year': 2023,
                'grade': 'A'
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 5: Add major (4/6 complete)
        self.stdout.write('\n--- Test 5: Add Major (4/6 complete) ---')
        student.major = dept
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 6: Complete admission test (5/6 complete)
        self.stdout.write('\n--- Test 6: Complete Admission Test (5/6 complete) ---')
        attempt, _ = AdmissionTestAttempt.objects.get_or_create(
            student=student,
            defaults={
                'is_completed': True,
                'total_score': 85,
                'max_possible_score': 100,
                'percentage_score': 85.0
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Test 7: Complete survey (6/6 complete - should unlock recommendations)
        self.stdout.write('\n--- Test 7: Complete Survey (6/6 complete) ---')
        survey, _ = StudentSurvey.objects.get_or_create(
            student=student,
            defaults={
                'learning_style': 'visual',
                'study_preference': 'group',
                'time_preference': 'morning',
                'motivation_factors': ['career_advancement', 'personal_interest'],
                'stress_level': 5,
                'extracurricular_time': 10,
                'work_hours': 15,
                'technology_comfort': 8,
                'career_certainty': 7
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Generate recommendations for complete profile
        from course_matcher.recommendation_service import RecommendationEngine
        engine = RecommendationEngine()
        recommendations = engine.get_recommendations(student)
        self.stdout.write(f'Generated {len(recommendations)} recommendations for complete profile')
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=False)
        
        # Test 8: Remove major to make profile incomplete again (5/6 complete)
        self.stdout.write('\n--- Test 8: Remove Major - Profile Incomplete Again (5/6 complete) ---')
        student.major = None
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        response = client.get('/recommendations/')
        self.check_recommendations_access(response, status, expected_blocked=True)
        
        # Cleanup
        self.stdout.write('\nCleaning up test data...')
        AcademicRecord.objects.filter(student=student).delete()
        AdmissionTestAttempt.objects.filter(student=student).delete()
        StudentSurvey.objects.filter(student=student).delete()
        student.delete()
        user.delete()
        
        self.stdout.write(self.style.SUCCESS('Profile completion synchronization test completed!'))
    
    def print_status(self, status):
        """Print the profile completion status in a readable format"""
        for component, completed in status.items():
            icon = '✅' if completed else '❌'
            self.stdout.write(f'  {icon} {component}: {completed}')
        
        completed_count = sum(status.values())
        total_count = len(status)
        percentage = (completed_count / total_count) * 100
        self.stdout.write(f'  Overall: {completed_count}/{total_count} ({percentage:.0f}%)')
    
    def check_recommendations_access(self, response, status, expected_blocked):
        """Check if recommendations access matches expected behavior"""
        all_completed = all(status.values())
        
        if response.status_code != 200:
            self.stdout.write(f'❌ Unexpected response status: {response.status_code}')
            return
        
        content = response.content.decode('utf-8')
        
        if expected_blocked:
            # Should show incomplete profile message
            if 'Complete Your Profile to Unlock Recommendations' in content:
                self.stdout.write('✅ Correctly blocked - showing incomplete profile message')
            elif 'profile_incomplete' in str(response.context):
                self.stdout.write('✅ Correctly blocked - profile_incomplete flag set')
            else:
                self.stdout.write('❌ Should be blocked but recommendations are showing')
        else:
            # Should show recommendations
            if 'Your Top Course Recommendations' in content:
                self.stdout.write('✅ Correctly unblocked - showing recommendations')
            elif 'No Recommendations Available' in content:
                self.stdout.write('⚠️ Profile complete but no recommendations generated yet')
            else:
                self.stdout.write('❌ Should show recommendations but showing incomplete message')
