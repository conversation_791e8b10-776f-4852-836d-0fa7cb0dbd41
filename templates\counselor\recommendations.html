{% extends 'base.html' %}

{% block title %}Course Recommendations Review - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Course Recommendations Review</h1>
            <p class="text-gray-600">Review and approve ML-generated course recommendations</p>
        </div>
    </div>
</div>

<!-- Coming Soon Message -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-12 text-center">
    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"/>
        </svg>
    </div>
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Course Recommendation Review System</h2>
    <p class="text-lg text-gray-600 mb-6">This feature will allow you to:</p>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        <div class="bg-blue-50 p-6 rounded-lg">
            <h3 class="font-semibold text-blue-900 mb-2">Review ML Recommendations</h3>
            <p class="text-blue-700">Examine AI-generated course suggestions with confidence scores and reasoning</p>
        </div>
        <div class="bg-green-50 p-6 rounded-lg">
            <h3 class="font-semibold text-green-900 mb-2">Approve or Modify</h3>
            <p class="text-green-700">Accept, reject, or modify recommendations before they reach students</p>
        </div>
        <div class="bg-purple-50 p-6 rounded-lg">
            <h3 class="font-semibold text-purple-900 mb-2">Add Counselor Notes</h3>
            <p class="text-purple-700">Provide additional context and guidance for each recommendation</p>
        </div>
        <div class="bg-orange-50 p-6 rounded-lg">
            <h3 class="font-semibold text-orange-900 mb-2">Track Approval History</h3>
            <p class="text-orange-700">Monitor your recommendation review history and outcomes</p>
        </div>
    </div>
    <div class="mt-8">
        <a href="{% url 'counselor_dashboard' %}" 
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
            Return to Dashboard
        </a>
    </div>
</div>
{% endblock %}
