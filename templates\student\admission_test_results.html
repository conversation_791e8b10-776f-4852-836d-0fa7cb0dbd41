{% extends 'student/base.html' %}

{% block title %}Admission Test Results - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Admission Test Results</h1>
        <p class="text-lg text-gray-600">Your comprehensive assessment results and performance analysis.</p>
    </div>

    <!-- Overall Score Card -->
    <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl shadow-lg border border-primary-200 p-8 mb-8">
        <div class="text-center">
            <div class="mb-4">
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full mb-4">
                    <span class="text-3xl font-bold text-white">{{ attempt.percentage_score|floatformat:0 }}%</span>
                </div>
            </div>
            <h2 class="text-2xl font-bold text-primary-800 mb-2">Overall Score</h2>
            <p class="text-primary-700 mb-4">{{ attempt.total_score }} out of {{ attempt.max_possible_score }} points</p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                <div class="text-center">
                    <div class="text-lg font-semibold text-primary-800">Completion Time</div>
                    <div class="text-2xl font-bold text-primary-600">{{ attempt.time_taken_minutes }} min</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-primary-800">Test Date</div>
                    <div class="text-2xl font-bold text-primary-600">{{ attempt.completed_at|date:"M d, Y" }}</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-primary-800">Performance</div>
                    <div class="text-2xl font-bold text-primary-600">
                        {% if attempt.percentage_score >= 90 %}Excellent
                        {% elif attempt.percentage_score >= 80 %}Very Good
                        {% elif attempt.percentage_score >= 70 %}Good
                        {% elif attempt.percentage_score >= 60 %}Fair
                        {% else %}Needs Improvement
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Subject-wise Performance -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6">Subject-wise Performance</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for subject, data in subject_results.items %}
            <div class="bg-gradient-to-br from-white to-gray-50 rounded-lg p-6 border border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-semibold text-gray-900 capitalize">{{ subject|title }}</h4>
                    <span class="text-2xl font-bold {% if data.percentage >= 80 %}text-green-600{% elif data.percentage >= 60 %}text-yellow-600{% else %}text-red-600{% endif %}">
                        {{ data.percentage|floatformat:0 }}%
                    </span>
                </div>
                
                <!-- Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                    <div class="{% if data.percentage >= 80 %}bg-green-500{% elif data.percentage >= 60 %}bg-yellow-500{% else %}bg-red-500{% endif %} h-3 rounded-full transition-all duration-500" 
                         style="width: {{ data.percentage }}%"></div>
                </div>
                
                <div class="space-y-2 text-sm text-gray-600">
                    <div class="flex justify-between">
                        <span>Correct Answers:</span>
                        <span class="font-medium">{{ data.correct_answers }}/{{ data.total_questions }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span>Points Earned:</span>
                        <span class="font-medium">{{ data.earned_points }}/{{ data.total_points }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Performance Insights -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-900 mb-6">Performance Insights</h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Strengths -->
            <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-6">
                <h4 class="font-semibold text-green-800 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Strengths
                </h4>
                <ul class="space-y-2">
                    {% for subject, data in subject_results.items %}
                        {% if data.percentage >= 75 %}
                        <li class="flex items-center text-green-700">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                            Strong performance in {{ subject|title }} ({{ data.percentage|floatformat:0 }}%)
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if attempt.time_taken_minutes <= 30 %}
                    <li class="flex items-center text-green-700">
                        <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                        Efficient time management
                    </li>
                    {% endif %}
                </ul>
            </div>
            
            <!-- Areas for Improvement -->
            <div class="bg-gradient-to-br from-amber-50 to-amber-100 rounded-lg p-6">
                <h4 class="font-semibold text-amber-800 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    Areas for Improvement
                </h4>
                <ul class="space-y-2">
                    {% for subject, data in subject_results.items %}
                        {% if data.percentage < 60 %}
                        <li class="flex items-center text-amber-700">
                            <span class="w-2 h-2 bg-amber-500 rounded-full mr-3"></span>
                            Focus on {{ subject|title }} ({{ data.percentage|floatformat:0 }}%)
                        </li>
                        {% endif %}
                    {% endfor %}
                    {% if attempt.time_taken_minutes > 60 %}
                    <li class="flex items-center text-amber-700">
                        <span class="w-2 h-2 bg-amber-500 rounded-full mr-3"></span>
                        Consider improving time management
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <!-- Recommendations -->
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-6 mb-8">
        <h3 class="text-xl font-semibold text-blue-800 mb-4 flex items-center">
            <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            Course Recommendations
        </h3>
        <p class="text-blue-700 mb-4">Based on your test performance, we recommend focusing on courses that align with your strengths while addressing areas for improvement.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for subject, data in subject_results.items %}
                {% if data.percentage >= 70 %}
                <div class="bg-white rounded-lg p-4 border border-blue-200">
                    <h4 class="font-medium text-blue-800">{{ subject|title }}-related Courses</h4>
                    <p class="text-sm text-blue-600">Your strong performance suggests you're ready for advanced {{ subject }} courses.</p>
                </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a href="{% url 'student_dashboard' %}" 
           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
        <a href="{% url 'student_survey' %}" 
           class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Continue to Survey
        </a>
    </div>
</div>

<style>
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
</style>
{% endblock %}
