{% extends 'base.html' %}

{% block title %}Preview Import - Admission Test Questions{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block extra_css %}
<style>
    .preview-table {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 0.75rem;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .question-preview {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 0.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }
    
    .question-preview:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .question-preview.selected {
        border-color: #3b82f6;
        background: rgba(59, 130, 246, 0.05);
    }
    
    .option-badge {
        background: rgba(59, 130, 246, 0.1);
        color: #1e40af;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        margin: 0.125rem;
        display: inline-block;
    }
    
    .correct-option {
        background: rgba(16, 185, 129, 0.2);
        color: #047857;
        font-weight: 600;
    }
    
    .subject-badge {
        background: rgba(139, 92, 246, 0.1);
        color: #6b21a8;
    }
    
    .difficulty-badge {
        background: rgba(245, 158, 11, 0.1);
        color: #92400e;
    }
    
    .select-all-section {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 0.75rem;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-2xl font-semibold text-gray-800">Preview Import Questions</h2>
                <p class="text-gray-600 mt-1">Review and select questions to import ({{ total_questions }} questions found)</p>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'admin_admission_test_import' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Import
                </a>
            </div>
        </div>
    </div>

    <!-- Selection Controls -->
    <div class="select-all-section p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <label class="flex items-center">
                    <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    <span class="ml-2 text-sm font-medium text-gray-700">Select All Questions</span>
                </label>
                <span id="selectedCount" class="text-sm text-gray-600">0 selected</span>
            </div>
            <div class="text-sm text-gray-600">
                Total: {{ total_questions }} questions
            </div>
        </div>
    </div>

    <!-- Import Form -->
    <form method="post" id="confirmImportForm">
        {% csrf_token %}
        
        <!-- Questions Preview -->
        <div class="space-y-4">
            {% for question in questions_data %}
            <div class="question-preview p-6" data-index="{{ forloop.counter0 }}">
                <div class="flex items-start space-x-4">
                    <!-- Selection Checkbox -->
                    <div class="flex-shrink-0 pt-1">
                        <input type="checkbox" name="selected_questions" value="{{ forloop.counter0 }}" 
                               class="question-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                    </div>
                    
                    <!-- Question Content -->
                    <div class="flex-1">
                        <!-- Question Header -->
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-lg font-medium text-gray-900">Question {{ forloop.counter }}</h3>
                            <div class="flex space-x-2">
                                <span class="subject-badge option-badge">{{ question.subject_area|title }}</span>
                                <span class="difficulty-badge option-badge">{{ question.difficulty_level|title }}</span>
                                <span class="option-badge">{{ question.points }} point{{ question.points|pluralize }}</span>
                            </div>
                        </div>
                        
                        <!-- Question Text -->
                        <div class="mb-4">
                            <p class="text-gray-800 font-medium">{{ question.question_text }}</p>
                        </div>
                        
                        <!-- Answer Options -->
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Answer Options:</h4>
                            <div class="flex flex-wrap gap-2">
                                {% for option in question.answer_options %}
                                <span class="option-badge {% if option == question.correct_answer %}correct-option{% endif %}">
                                    {{ forloop.counter }}. {{ option }}
                                    {% if option == question.correct_answer %}✓{% endif %}
                                </span>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <!-- Explanation -->
                        {% if question.explanation %}
                        <div class="mb-2">
                            <h4 class="text-sm font-medium text-gray-700 mb-1">Explanation:</h4>
                            <p class="text-sm text-gray-600">{{ question.explanation }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Import Button -->
        <div class="mt-8 flex justify-center">
            <button type="submit" name="confirm_import" id="confirmImportBtn" disabled
                    class="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-bold py-3 px-8 rounded-lg shadow-md transition-all duration-300 inline-flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span id="importBtnText">Import Selected Questions</span>
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const questionCheckboxes = document.querySelectorAll('.question-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const confirmImportBtn = document.getElementById('confirmImportBtn');
    const importBtnText = document.getElementById('importBtnText');
    const questionPreviews = document.querySelectorAll('.question-preview');

    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.question-checkbox:checked').length;
        selectedCountSpan.textContent = `${selectedCount} selected`;
        
        // Update button state
        if (selectedCount > 0) {
            confirmImportBtn.disabled = false;
            importBtnText.textContent = `Import ${selectedCount} Question${selectedCount !== 1 ? 's' : ''}`;
        } else {
            confirmImportBtn.disabled = true;
            importBtnText.textContent = 'Import Selected Questions';
        }
        
        // Update select all checkbox state
        if (selectedCount === questionCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCount > 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
    }

    // Select all functionality
    selectAllCheckbox.addEventListener('change', function() {
        questionCheckboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
            updateQuestionPreviewStyle(checkbox);
        });
        updateSelectedCount();
    });

    // Individual checkbox functionality
    questionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateQuestionPreviewStyle(checkbox);
            updateSelectedCount();
        });
    });

    function updateQuestionPreviewStyle(checkbox) {
        const questionPreview = checkbox.closest('.question-preview');
        if (checkbox.checked) {
            questionPreview.classList.add('selected');
        } else {
            questionPreview.classList.remove('selected');
        }
    }

    // Initialize
    updateSelectedCount();
    
    // Select all by default
    selectAllCheckbox.checked = true;
    selectAllCheckbox.dispatchEvent(new Event('change'));
});
</script>
{% endblock %}
