{% extends 'student/base.html' %}

{% block title %}Profile Management - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">

    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">Profile Management</h1>
        <p class="text-gray-600">Manage and update your profile information</p>
    </div>

    <!-- Profile Status -->
    {% if all_completed %}
    <div class="mb-8 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-green-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
            <div class="flex-1">
                <h3 class="text-sm font-medium text-green-800">🎉 Profile Complete!</h3>
                <p class="text-xs text-green-600">Your profile is complete. You can edit any section below.</p>
            </div>
            <a href="{% url 'recommendations' %}"
               class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors duration-200">
                View Recommendations
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Profile Sections Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

        <!-- Basic Information -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Basic Information</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Profile
                </span>
            </div>
            <div class="space-y-3">
                <div>
                    <label class="text-sm font-medium text-gray-700">Name</label>
                    <p class="text-gray-900">{{ student.user.get_full_name }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700">Student ID</label>
                    <p class="text-gray-900">{{ student.student_id }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700">Email</label>
                    <p class="text-gray-900">{{ student.user.email }}</p>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700">Year</label>
                    <p class="text-gray-900">{{ student.get_year_display }}</p>
                </div>
                {% if student.phone_number %}
                <div>
                    <label class="text-sm font-medium text-gray-700">Phone</label>
                    <p class="text-gray-900">{{ student.phone_number }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Academic Major -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Academic Major</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.major %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.major %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if student.major %}
                <div>
                    <label class="text-sm font-medium text-gray-700">Current Major</label>
                    <p class="text-gray-900">{{ student.major.name }}</p>
                    <p class="text-sm text-gray-500">{{ student.major.code }}</p>
                </div>
                {% else %}
                <p class="text-amber-600">No major selected</p>
                {% endif %}
                <a href="{% url 'select_major' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if student.major %}Change Major{% else %}Select Major{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Academic Interests -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Academic Interests</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.interests %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.interests %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if student.interests %}
                <div class="flex flex-wrap gap-2">
                    {% for interest in student.interests %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                        {{ interest }}
                    </span>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-amber-600">No interests added</p>
                {% endif %}
                <a href="{% url 'student_interests' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if student.interests %}Edit Interests{% else %}Add Interests{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Career Goals -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Career Goals</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.career_goals %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.career_goals %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if student.career_goals %}
                <div>
                    <p class="text-gray-900">{{ student.career_goals|truncatewords:30 }}</p>
                </div>
                {% else %}
                <p class="text-amber-600">No career goals specified</p>
                {% endif %}
                <a href="{% url 'career_goals' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if student.career_goals %}Edit Goals{% else %}Add Goals{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Academic Records -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Academic Records</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.academic_records %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.academic_records %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if academic_records %}
                <div class="space-y-2">
                    {% for record in academic_records|slice:":3" %}
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-900">{{ record.course.code }}</span>
                        {% if record.grade %}
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            {{ record.grade }}
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            In Progress
                        </span>
                        {% endif %}
                    </div>
                    {% endfor %}
                    {% if academic_records.count > 3 %}
                    <p class="text-xs text-gray-500">And {{ academic_records.count|add:"-3" }} more...</p>
                    {% endif %}
                </div>
                {% else %}
                <p class="text-amber-600">No academic records</p>
                {% endif %}
                <a href="{% url 'academic_records' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if academic_records %}Manage Records{% else %}Add Records{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Admission Test -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Admission Test</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.admission_test %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.admission_test %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if admission_attempts %}
                {% with latest_attempt=admission_attempts.first %}
                <div>
                    <p class="text-sm text-gray-700">Latest Score</p>
                    <p class="text-2xl font-bold text-gray-900">{{ latest_attempt.percentage_score|floatformat:1 }}%</p>
                    <p class="text-xs text-gray-500">{{ latest_attempt.started_at|date:"M d, Y" }}</p>
                </div>
                {% endwith %}
                {% else %}
                <p class="text-amber-600">No test attempts</p>
                {% endif %}
                <a href="{% url 'admission_test_start' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if admission_attempts %}Retake Test{% else %}Take Test{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Learning Survey -->
        <div class="glassmorphism rounded-xl p-6 border border-white/20">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-900">Learning Survey</h2>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if progress.survey %}bg-green-100 text-green-800{% else %}bg-amber-100 text-amber-800{% endif %}">
                    {% if progress.survey %}Complete{% else %}Required{% endif %}
                </span>
            </div>
            <div class="space-y-4">
                {% if survey %}
                <div>
                    <p class="text-sm text-gray-700">Learning Style</p>
                    <p class="text-gray-900">{{ survey.get_learning_style_display }}</p>
                    <p class="text-xs text-gray-500">Completed {{ survey.completed_at|date:"M d, Y" }}</p>
                </div>
                {% else %}
                <p class="text-amber-600">Survey not completed</p>
                {% endif %}
                <a href="{% url 'student_survey' %}"
                   class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transition-all duration-200">
                    {% if survey %}Update Survey{% else %}Take Survey{% endif %}
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

    </div>

    <!-- Action Buttons -->
    <div class="mt-8 flex justify-between">
        <a href="{% url 'student_dashboard' %}"
           class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
        </a>
        
        {% if all_completed %}
        <a href="{% url 'recommendations' %}"
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 transition-all duration-200 shadow-lg">
            View Recommendations
            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
        </a>
        {% endif %}
    </div>

</div>
{% endblock %}
