from django import forms
from django.contrib.auth.models import User
from .models import StudentProfile, Department, AcademicRecord, Course, AdmissionTest, AdmissionTestAnswer, StudentSurvey
import json

class StudentRegistrationForm(forms.ModelForm):
    username = forms.CharField(max_length=150, required=True, widget=forms.TextInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your username'}))
    email = forms.EmailField(required=True, widget=forms.EmailInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your email address'}))
    password = forms.Char<PERSON>ield(widget=forms.PasswordInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your password'}), required=True)
    confirm_password = forms.CharField(widget=forms.PasswordInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Confirm your password'}), required=True)
    first_name = forms.CharField(max_length=30, required=True, widget=forms.TextInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your first name'}))
    last_name = forms.CharField(max_length=30, required=True, widget=forms.TextInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your last name'}))

    class Meta:
        model = StudentProfile
        fields = ['student_id', 'year', 'major', 'date_of_birth', 'phone_number', 'address', 
                  'expected_graduation_year', 'career_goals', 'preferred_difficulty']
        widgets = {
            'student_id': forms.TextInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your student ID'}),
            'year': forms.Select(attrs={'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'}),
            'major': forms.Select(attrs={'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'}),
            'career_goals': forms.Textarea(attrs={'class': 'form-textarea w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'rows': 3, 'placeholder': 'Describe your career goals and aspirations...'}),
            'preferred_difficulty': forms.Select(attrs={'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'type': 'date'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'Enter your phone number'}),
            'address': forms.Textarea(attrs={'class': 'form-textarea w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'rows': 3, 'placeholder': 'Enter your address...'}),
            'expected_graduation_year': forms.NumberInput(attrs={'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500', 'placeholder': 'e.g., 2026', 'min': '2024', 'max': '2040'}),
        }

    def __init__(self, *args, **kwargs):
        super(StudentRegistrationForm, self).__init__(*args, **kwargs)
        self.fields['major'].queryset = Department.objects.all()
        self.fields['major'].required = False

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError("A user with that username already exists.")
        return username

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("A user with that email address already exists.")
        return email

    def clean_confirm_password(self):
        password = self.cleaned_data.get('password')
        confirm_password = self.cleaned_data.get('confirm_password')
        if password and confirm_password and password != confirm_password:
            raise forms.ValidationError("Passwords do not match.")
        return confirm_password

    def clean_student_id(self):
        student_id = self.cleaned_data.get('student_id')
        if StudentProfile.objects.filter(student_id=student_id).exists():
            raise forms.ValidationError("A student with this ID already exists.")
        return student_id

    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get("password")
        confirm_password = cleaned_data.get("confirm_password")

        if password and confirm_password and password != confirm_password:
            self.add_error('confirm_password', "Passwords do not match.")

        return cleaned_data


class AcademicRecordForm(forms.ModelForm):
    """Form for adding/editing academic records (course history)"""

    class Meta:
        model = AcademicRecord
        fields = ['course', 'semester', 'year', 'grade']
        widgets = {
            'course': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'required': True
            }),
            'semester': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'required': True
            }),
            'year': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'e.g., 2024',
                'min': '2020',
                'max': '2030',
                'required': True
            }),
            'grade': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
        }

    def __init__(self, *args, **kwargs):
        self.student = kwargs.pop('student', None)
        super().__init__(*args, **kwargs)

        # Filter out courses already taken by this student
        if self.student:
            taken_courses = AcademicRecord.objects.filter(student=self.student).values_list('course_id', flat=True)
            self.fields['course'].queryset = Course.objects.exclude(id__in=taken_courses)
        else:
            self.fields['course'].queryset = Course.objects.all()

    def clean(self):
        cleaned_data = super().clean()
        course = cleaned_data.get('course')

        # Check if student already has a record for this course
        if self.student and course:
            existing = AcademicRecord.objects.filter(student=self.student, course=course)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise forms.ValidationError(f"You already have a record for {course.code}.")

        return cleaned_data


class StudentInterestsForm(forms.Form):
    """Form for managing student interests and preferences"""

    interests = forms.CharField(
        widget=forms.HiddenInput(),
        required=False,
        help_text="JSON array of interests"
    )

    new_interest = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
            'placeholder': 'Add a new interest (e.g., Machine Learning, Web Development)',
            'id': 'new-interest-input'
        })
    )

    def __init__(self, *args, **kwargs):
        self.student = kwargs.pop('student', None)
        super().__init__(*args, **kwargs)

        # Pre-populate with existing interests
        if self.student and hasattr(self.student, 'interests'):
            self.fields['interests'].initial = json.dumps(self.student.interests or [])

    def clean_interests(self):
        interests_json = self.cleaned_data.get('interests', '[]')
        try:
            interests = json.loads(interests_json)
            if not isinstance(interests, list):
                raise forms.ValidationError("Interests must be a list.")

            # Validate each interest
            for interest in interests:
                if not isinstance(interest, str) or len(interest.strip()) == 0:
                    raise forms.ValidationError("Each interest must be a non-empty string.")
                if len(interest) > 100:
                    raise forms.ValidationError("Each interest must be 100 characters or less.")

            return interests
        except json.JSONDecodeError:
            raise forms.ValidationError("Invalid interests format.")

    def save(self):
        if self.student:
            interests = self.cleaned_data.get('interests', [])
            new_interest = self.cleaned_data.get('new_interest', '').strip()

            # Add new interest if provided
            if new_interest and new_interest not in interests:
                interests.append(new_interest)

            # Update student profile
            self.student.interests = interests
            self.student.save()
            return self.student
        return None


class CareerGoalsForm(forms.ModelForm):
    """Form for updating career objectives and goals"""

    class Meta:
        model = StudentProfile
        fields = ['career_goals', 'preferred_difficulty', 'expected_graduation_year']
        widgets = {
            'career_goals': forms.Textarea(attrs={
                'class': 'form-textarea w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'rows': 6,
                'placeholder': 'Describe your career goals, aspirations, and what you hope to achieve after graduation...'
            }),
            'preferred_difficulty': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
            'expected_graduation_year': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'placeholder': 'e.g., 2026',
                'min': '2024',
                'max': '2040'
            }),
        }

    def clean_career_goals(self):
        career_goals = self.cleaned_data.get('career_goals', '').strip()
        if len(career_goals) > 2000:
            raise forms.ValidationError("Career goals must be 2000 characters or less.")
        return career_goals

    def clean_expected_graduation_year(self):
        year = self.cleaned_data.get('expected_graduation_year')
        if year and (year < 2024 or year > 2040):
            raise forms.ValidationError("Expected graduation year must be between 2024 and 2040.")
        return year


class AdmissionTestForm(forms.Form):
    """Dynamic form for admission test questions"""

    def __init__(self, questions, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.questions = questions

        for question in questions:
            field_name = f'question_{question.id}'

            if question.question_type == 'multiple_choice':
                choices = [(option, option) for option in question.options]
                self.fields[field_name] = forms.ChoiceField(
                    choices=choices,
                    widget=forms.RadioSelect(attrs={
                        'class': 'form-radio text-primary-600 focus:ring-primary-500'
                    }),
                    label=question.question_text,
                    required=True
                )
            elif question.question_type == 'true_false':
                self.fields[field_name] = forms.ChoiceField(
                    choices=[('true', 'True'), ('false', 'False')],
                    widget=forms.RadioSelect(attrs={
                        'class': 'form-radio text-primary-600 focus:ring-primary-500'
                    }),
                    label=question.question_text,
                    required=True
                )
            else:  # short_answer
                self.fields[field_name] = forms.CharField(
                    widget=forms.Textarea(attrs={
                        'class': 'form-textarea w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                        'rows': 3,
                        'placeholder': 'Enter your answer here...'
                    }),
                    label=question.question_text,
                    required=True
                )

    def get_answers(self):
        """Return a dictionary of question_id: answer pairs"""
        answers = {}
        for question in self.questions:
            field_name = f'question_{question.id}'
            if field_name in self.cleaned_data:
                answers[question.id] = self.cleaned_data[field_name]
        return answers


class StudentSurveyForm(forms.ModelForm):
    """Form for student learning preferences and style survey"""

    motivation_factors = forms.MultipleChoiceField(
        choices=[
            ('career_advancement', 'Career Advancement'),
            ('personal_interest', 'Personal Interest'),
            ('family_expectations', 'Family Expectations'),
            ('financial_security', 'Financial Security'),
            ('intellectual_challenge', 'Intellectual Challenge'),
            ('helping_others', 'Helping Others'),
            ('creativity', 'Creativity and Innovation'),
            ('leadership', 'Leadership Opportunities'),
            ('work_life_balance', 'Work-Life Balance'),
            ('social_impact', 'Making a Social Impact'),
        ],
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-checkbox text-primary-600 focus:ring-primary-500'
        }),
        required=True,
        help_text="Select all factors that motivate you (choose at least 2)"
    )

    class Meta:
        model = StudentSurvey
        fields = [
            'learning_style', 'study_preference', 'time_preference', 'motivation_factors',
            'preferred_course_format', 'stress_level', 'extracurricular_time', 'work_hours',
            'technology_comfort', 'career_certainty', 'additional_comments'
        ]
        widgets = {
            'learning_style': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
            'study_preference': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
            'time_preference': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
            'preferred_course_format': forms.Select(attrs={
                'class': 'form-select w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500'
            }),
            'stress_level': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'min': '1', 'max': '10', 'step': '1'
            }),
            'extracurricular_time': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'min': '0', 'max': '40', 'step': '1'
            }),
            'work_hours': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'min': '0', 'max': '40', 'step': '1'
            }),
            'technology_comfort': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'min': '1', 'max': '10', 'step': '1'
            }),
            'career_certainty': forms.NumberInput(attrs={
                'class': 'form-input w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'min': '1', 'max': '10', 'step': '1'
            }),
            'additional_comments': forms.Textarea(attrs={
                'class': 'form-textarea w-full px-4 py-2 border rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500',
                'rows': 4,
                'placeholder': 'Any additional comments about your learning preferences or goals...'
            }),
        }

    def clean_motivation_factors(self):
        factors = self.cleaned_data.get('motivation_factors', [])
        if len(factors) < 2:
            raise forms.ValidationError("Please select at least 2 motivation factors.")
        return factors

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Convert motivation_factors list to JSON for storage
        instance.motivation_factors = self.cleaned_data.get('motivation_factors', [])
        if commit:
            instance.save()
        return instance
