<table class="min-w-full bg-white">
    <thead class="bg-gray-100">
        <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Difficulty</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for course in courses %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ course.code }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ course.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ course.department.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ course.credits }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ course.get_difficulty_display }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2">
                        <a href="{% url 'admin_course_view' course.id %}" class="text-primary-600 hover:text-primary-900 font-medium">View</a>
                        <a href="{% url 'admin_course_edit' course.id %}" class="text-green-600 hover:text-green-900 font-medium">Edit</a>
                        <a href="{% url 'admin_course_delete' course.id %}" class="text-red-600 hover:text-red-900 font-medium">Delete</a>
                    </div>
                </td>
            </tr>
        {% empty %}
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">No courses found.</td>
            </tr>
        {% endfor %}
    </tbody>
</table>
