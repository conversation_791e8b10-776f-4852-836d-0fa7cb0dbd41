from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from course_matcher.models import StudentProfile, AcademicRecord, AdmissionTestAttempt, StudentSurvey, Course, Department
from course_matcher.views import get_profile_completion_status


class Command(BaseCommand):
    help = 'Test profile completion logic'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing profile completion logic...'))
        
        # Create or get a test user
        user, created = User.objects.get_or_create(
            username='test_student',
            defaults={
                'first_name': 'Test',
                'last_name': 'Student',
                'email': '<EMAIL>'
            }
        )
        
        # Create or get student profile
        student, created = StudentProfile.objects.get_or_create(
            user=user,
            defaults={
                'student_id': 'TEST001',
                'year': 'sophomore',
                'interests': [],
                'career_goals': '',
            }
        )
        
        self.stdout.write(f'Testing with student: {student}')
        
        # Test 1: Empty profile
        self.stdout.write('\n--- Test 1: Empty Profile ---')
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 2: Add interests (empty list should be False)
        self.stdout.write('\n--- Test 2: Empty interests list ---')
        student.interests = []
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 3: Add interests (non-empty list should be True)
        self.stdout.write('\n--- Test 3: Non-empty interests list ---')
        student.interests = ['Machine Learning', 'Web Development']
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 4: Add career goals (empty string should be False)
        self.stdout.write('\n--- Test 4: Empty career goals ---')
        student.career_goals = ''
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 5: Add career goals (whitespace only should be False)
        self.stdout.write('\n--- Test 5: Whitespace-only career goals ---')
        student.career_goals = '   \n\t   '
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 6: Add career goals (real content should be True)
        self.stdout.write('\n--- Test 6: Real career goals ---')
        student.career_goals = 'I want to become a software engineer specializing in AI.'
        student.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 7: Add academic record
        self.stdout.write('\n--- Test 7: Add academic record ---')
        dept, _ = Department.objects.get_or_create(
            code='CS',
            defaults={'name': 'Computer Science'}
        )
        course, _ = Course.objects.get_or_create(
            code='CS101',
            defaults={
                'title': 'Introduction to Programming',
                'description': 'Basic programming concepts',
                'credits': 3,
                'department': dept,
                'difficulty': 'beginner'
            }
        )
        record, _ = AcademicRecord.objects.get_or_create(
            student=student,
            course=course,
            defaults={
                'semester': 'fall',
                'year': 2023,
                'grade': 'A'
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 8: Add admission test attempt (incomplete)
        self.stdout.write('\n--- Test 8: Incomplete admission test ---')
        attempt, _ = AdmissionTestAttempt.objects.get_or_create(
            student=student,
            defaults={
                'is_completed': False,
                'total_score': 0,
                'max_possible_score': 100
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 9: Complete admission test
        self.stdout.write('\n--- Test 9: Complete admission test ---')
        attempt.is_completed = True
        attempt.total_score = 85
        attempt.percentage_score = 85.0
        attempt.save()
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Test 10: Add survey
        self.stdout.write('\n--- Test 10: Add survey ---')
        survey, _ = StudentSurvey.objects.get_or_create(
            student=student,
            defaults={
                'learning_style': 'visual',
                'study_preference': 'group',
                'time_preference': 'morning',
                'motivation_factors': ['career_advancement', 'personal_interest'],
                'stress_level': 5,
                'extracurricular_time': 10,
                'work_hours': 15,
                'technology_comfort': 8,
                'career_certainty': 7
            }
        )
        status = get_profile_completion_status(student)
        self.print_status(status)
        
        # Final test: All completed
        self.stdout.write('\n--- Final Test: All Components Complete ---')
        all_completed = all(status.values())
        self.stdout.write(f'All completed: {all_completed}')
        
        if all_completed:
            self.stdout.write(self.style.SUCCESS('✅ Profile completion logic working correctly!'))
        else:
            self.stdout.write(self.style.ERROR('❌ Profile completion logic has issues!'))
        
        # Cleanup
        self.stdout.write('\nCleaning up test data...')
        AcademicRecord.objects.filter(student=student).delete()
        AdmissionTestAttempt.objects.filter(student=student).delete()
        StudentSurvey.objects.filter(student=student).delete()
        student.delete()
        user.delete()
        
        self.stdout.write(self.style.SUCCESS('Test completed!'))
    
    def print_status(self, status):
        """Print the profile completion status in a readable format"""
        for component, completed in status.items():
            icon = '✅' if completed else '❌'
            self.stdout.write(f'  {icon} {component}: {completed}')
        
        completed_count = sum(status.values())
        total_count = len(status)
        percentage = (completed_count / total_count) * 100
        self.stdout.write(f'  Overall: {completed_count}/{total_count} ({percentage:.0f}%)')
