{% extends 'student/base.html' %}

{% block title %}Taking Admission Test - Course Recommendation System{% endblock %}

{% block content %}
<div class="max-w-5xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header with Progress -->
    <div class="mb-8">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-3xl font-bold text-gray-900">Admission Test</h1>
            <div class="text-sm text-gray-500" id="timer">
                <svg class="w-4 h-4 inline mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                </svg>
                <span id="elapsed-time">00:00</span>
            </div>
        </div>
        
        <!-- Progress Bar -->
        <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-300" 
                 style="width: 0%" id="progress-bar"></div>
        </div>
        <p class="text-gray-600">Answer all questions to the best of your ability. You can review your answers before submitting.</p>
    </div>

    <form method="post" id="test-form">
        {% csrf_token %}
        
        <!-- Questions Container -->
        <div class="space-y-8">
            {% for question in questions %}
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 question-card" 
                 data-question-id="{{ question.id }}">
                
                <!-- Question Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                {{ forloop.counter }}
                            </div>
                        </div>
                        <div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-{{ question.subject_area|default:'gray' }}-100 text-{{ question.subject_area|default:'gray' }}-800">
                                {{ question.get_subject_area_display }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 ml-2">
                                {{ question.get_difficulty_level_display }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
                                {{ question.points }} point{{ question.points|pluralize }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Question Text -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-3">{{ question.question_text }}</h3>
                </div>

                <!-- Answer Options -->
                <div class="space-y-3">
                    {% if question.question_type == 'multiple_choice' %}
                        {% for option in question.options %}
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                            <input type="radio" name="question_{{ question.id }}" value="{{ option }}" 
                                   class="form-radio text-primary-600 focus:ring-primary-500 mr-3" required>
                            <span class="text-gray-700">{{ option }}</span>
                        </label>
                        {% endfor %}
                    {% elif question.question_type == 'true_false' %}
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                            <input type="radio" name="question_{{ question.id }}" value="true" 
                                   class="form-radio text-primary-600 focus:ring-primary-500 mr-3" required>
                            <span class="text-gray-700">True</span>
                        </label>
                        <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-200">
                            <input type="radio" name="question_{{ question.id }}" value="false" 
                                   class="form-radio text-primary-600 focus:ring-primary-500 mr-3" required>
                            <span class="text-gray-700">False</span>
                        </label>
                    {% else %}
                        <textarea name="question_{{ question.id }}" rows="4" required
                                  class="form-textarea w-full px-4 py-2 border border-gray-200 rounded-lg text-gray-700 focus:ring-primary-500 focus:border-primary-500"
                                  placeholder="Enter your answer here..."></textarea>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Submit Section -->
        <div class="mt-12 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6">
            <div class="text-center">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Ready to Submit?</h3>
                <p class="text-gray-600 mb-6">Please review your answers before submitting. You cannot change them after submission.</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button type="button" id="review-btn" 
                            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        Review Answers
                    </button>
                    <button type="submit" id="submit-btn"
                            class="inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5">
                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Submit Test
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Timer functionality
    const startTime = new Date();
    const timerElement = document.getElementById('elapsed-time');
    
    function updateTimer() {
        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        timerElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    setInterval(updateTimer, 1000);
    
    // Progress tracking
    const questions = document.querySelectorAll('.question-card');
    const progressBar = document.getElementById('progress-bar');
    const totalQuestions = questions.length;
    
    function updateProgress() {
        let answeredCount = 0;
        questions.forEach(question => {
            const inputs = question.querySelectorAll('input[type="radio"], textarea');
            const isAnswered = Array.from(inputs).some(input => {
                if (input.type === 'radio') {
                    return input.checked;
                } else {
                    return input.value.trim() !== '';
                }
            });
            if (isAnswered) answeredCount++;
        });
        
        const progress = (answeredCount / totalQuestions) * 100;
        progressBar.style.width = `${progress}%`;
    }
    
    // Add event listeners for progress tracking
    document.addEventListener('change', updateProgress);
    document.addEventListener('input', updateProgress);
    
    // Review functionality
    document.getElementById('review-btn').addEventListener('click', function() {
        const unanswered = [];
        questions.forEach((question, index) => {
            const inputs = question.querySelectorAll('input[type="radio"], textarea');
            const isAnswered = Array.from(inputs).some(input => {
                if (input.type === 'radio') {
                    return input.checked;
                } else {
                    return input.value.trim() !== '';
                }
            });
            if (!isAnswered) {
                unanswered.push(index + 1);
            }
        });
        
        if (unanswered.length > 0) {
            alert(`Please answer the following questions: ${unanswered.join(', ')}`);
            // Scroll to first unanswered question
            const firstUnanswered = questions[unanswered[0] - 1];
            firstUnanswered.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
            alert('All questions have been answered. You can now submit the test.');
        }
    });
    
    // Form submission confirmation
    document.getElementById('test-form').addEventListener('submit', function(e) {
        if (!confirm('Are you sure you want to submit the test? You cannot change your answers after submission.')) {
            e.preventDefault();
        }
    });
});
</script>

<style>
    .backdrop-blur-sm {
        backdrop-filter: blur(4px);
    }
    
    .question-card {
        transition: all 0.3s ease;
    }
    
    .question-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}
