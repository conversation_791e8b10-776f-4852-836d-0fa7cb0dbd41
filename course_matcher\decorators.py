"""
Custom decorators for role-based access control in CourseRec
"""
from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from .models import GuidanceCounselorProfile, StudentProfile


def counselor_required(view_func):
    """
    Decorator that requires the user to be a guidance counselor.
    Redirects to appropriate dashboard if user has different role.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if is_guidance_counselor(request.user):
            return view_func(request, *args, **kwargs)
        elif is_student(request.user):
            messages.warning(request, "Access denied. This area is for guidance counselors only.")
            return redirect('student_dashboard')
        elif request.user.is_staff or request.user.is_superuser:
            messages.warning(request, "Access denied. This area is for guidance counselors only.")
            return redirect('management_dashboard')
        else:
            raise PermissionDenied("You don't have permission to access this area.")
    
    return _wrapped_view


def admin_required(view_func):
    """
    Decorator that requires the user to be an admin/staff member.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if request.user.is_staff or request.user.is_superuser:
            return view_func(request, *args, **kwargs)
        elif is_guidance_counselor(request.user):
            messages.warning(request, "Access denied. This area is for administrators only.")
            return redirect('counselor_dashboard')
        elif is_student(request.user):
            messages.warning(request, "Access denied. This area is for administrators only.")
            return redirect('student_dashboard')
        else:
            raise PermissionDenied("You don't have permission to access this area.")
    
    return _wrapped_view


def student_required(view_func):
    """
    Decorator that requires the user to be a student.
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        if is_student(request.user):
            return view_func(request, *args, **kwargs)
        elif is_guidance_counselor(request.user):
            messages.warning(request, "Access denied. This area is for students only.")
            return redirect('counselor_dashboard')
        elif request.user.is_staff or request.user.is_superuser:
            messages.warning(request, "Access denied. This area is for students only.")
            return redirect('management_dashboard')
        else:
            raise PermissionDenied("You don't have permission to access this area.")
    
    return _wrapped_view


def is_guidance_counselor(user):
    """
    Check if a user is a guidance counselor.
    """
    if not user.is_authenticated:
        return False
    
    try:
        return hasattr(user, 'guidancecounselorprofile') and user.guidancecounselorprofile.is_active
    except GuidanceCounselorProfile.DoesNotExist:
        return False


def is_student(user):
    """
    Check if a user is a student.
    """
    if not user.is_authenticated:
        return False
    
    try:
        return hasattr(user, 'studentprofile')
    except StudentProfile.DoesNotExist:
        return False


def is_admin(user):
    """
    Check if a user is an admin/staff member.
    """
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def get_user_role(user):
    """
    Get the primary role of a user.
    Returns: 'admin', 'counselor', 'student', or 'unknown'
    """
    if not user.is_authenticated:
        return 'unknown'
    
    if is_admin(user):
        return 'admin'
    elif is_guidance_counselor(user):
        return 'counselor'
    elif is_student(user):
        return 'student'
    else:
        return 'unknown'


def get_user_dashboard_url(user):
    """
    Get the appropriate dashboard URL for a user based on their role.
    """
    role = get_user_role(user)
    
    if role == 'admin':
        return 'management_dashboard'
    elif role == 'counselor':
        return 'counselor_dashboard'
    elif role == 'student':
        return 'student_dashboard'
    else:
        return 'landing_page'


def can_access_student_data(user, student):
    """
    Check if a user can access a specific student's data.
    Admins can access all students.
    Counselors can access assigned students.
    Students can only access their own data.
    """
    if not user.is_authenticated:
        return False
    
    # Admins can access all student data
    if is_admin(user):
        return True
    
    # Students can only access their own data
    if is_student(user):
        try:
            return user.studentprofile == student
        except StudentProfile.DoesNotExist:
            return False
    
    # Counselors can access assigned students
    if is_guidance_counselor(user):
        try:
            from .models import CounselorStudentAssignment
            return CounselorStudentAssignment.objects.filter(
                counselor=user.guidancecounselorprofile,
                student=student,
                is_active=True
            ).exists()
        except GuidanceCounselorProfile.DoesNotExist:
            return False
    
    return False


def can_modify_recommendations(user, student):
    """
    Check if a user can modify recommendations for a specific student.
    Only admins and assigned counselors can modify recommendations.
    """
    if not user.is_authenticated:
        return False
    
    # Admins can modify all recommendations
    if is_admin(user):
        return True
    
    # Counselors can modify recommendations for assigned students
    if is_guidance_counselor(user):
        return can_access_student_data(user, student)
    
    return False
