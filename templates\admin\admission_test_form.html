{% extends 'base.html' %}

{% block title %}{{ action }} Admission Test - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">{{ action }} Admission Test Question</h2>
            <p class="text-gray-600 mt-1">{% if action == 'Create' %}Add a new test question{% else %}Update test question details{% endif %}</p>
        </div>
        <a href="{% url 'management_admission_tests' %}" 
           class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition-transform transform hover:scale-105 inline-flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to List
        </a>
    </div>

    <!-- Form -->
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        <!-- Question Text -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Question Details</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="question_text" class="block text-sm font-medium text-gray-700 mb-2">
                        Question Text <span class="text-red-500">*</span>
                    </label>
                    <textarea name="question_text" id="question_text" rows="4" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                              placeholder="Enter the test question...">{% if test %}{{ test.question_text }}{% endif %}</textarea>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="subject_area" class="block text-sm font-medium text-gray-700 mb-2">
                            Subject Area <span class="text-red-500">*</span>
                        </label>
                        <select name="subject_area" id="subject_area" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                            <option value="">Select Subject</option>
                            {% for choice in subject_choices %}
                            <option value="{{ choice.0 }}" {% if test and test.subject_area == choice.0 %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="difficulty_level" class="block text-sm font-medium text-gray-700 mb-2">
                            Difficulty Level <span class="text-red-500">*</span>
                        </label>
                        <select name="difficulty_level" id="difficulty_level" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                            <option value="">Select Difficulty</option>
                            {% for choice in difficulty_choices %}
                            <option value="{{ choice.0 }}" {% if test and test.difficulty_level == choice.0 %}selected{% endif %}>
                                {{ choice.1 }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Answer Options -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Answer Options</h3>
            <p class="text-sm text-gray-600 mb-4">Provide at least 2 answer options. You can add up to 4 options.</p>
            
            <div class="space-y-4">
                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                            1
                        </span>
                    </div>
                    <div class="flex-1">
                        <input type="text" name="option_1" id="option_1" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                               placeholder="Enter option 1 (required)"
                               {% if test and test.answer_options.0 %}value="{{ test.answer_options.0 }}"{% endif %}>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                            2
                        </span>
                    </div>
                    <div class="flex-1">
                        <input type="text" name="option_2" id="option_2" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                               placeholder="Enter option 2 (required)"
                               {% if test and test.answer_options.1 %}value="{{ test.answer_options.1 }}"{% endif %}>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                            3
                        </span>
                    </div>
                    <div class="flex-1">
                        <input type="text" name="option_3" id="option_3"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                               placeholder="Enter option 3"
                               {% if test and test.answer_options.2 %}value="{{ test.answer_options.2 }}"{% endif %}>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="flex-shrink-0">
                        <span class="inline-flex items-center justify-center w-8 h-8 bg-primary-100 text-primary-800 rounded-full text-sm font-medium">
                            4
                        </span>
                    </div>
                    <div class="flex-1">
                        <input type="text" name="option_4" id="option_4"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                               placeholder="Enter option 4"
                               {% if test and test.answer_options.3 %}value="{{ test.answer_options.3 }}"{% endif %}>
                    </div>
                </div>
            </div>
        </div>

        <!-- Correct Answer and Settings -->
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Answer Configuration</h3>
            
            <div class="space-y-4">
                <div>
                    <label for="correct_answer" class="block text-sm font-medium text-gray-700 mb-2">
                        Correct Answer <span class="text-red-500">*</span>
                    </label>
                    <input type="text" name="correct_answer" id="correct_answer" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                           placeholder="Enter the correct answer exactly as it appears in the options above"
                           value="{% if test %}{{ test.correct_answer }}{% endif %}">
                    <p class="mt-1 text-sm text-gray-500">This should match exactly one of the options above.</p>
                </div>
                
                <div>
                    <label for="explanation" class="block text-sm font-medium text-gray-700 mb-2">
                        Explanation (Optional)
                    </label>
                    <textarea name="explanation" id="explanation" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                              placeholder="Provide an explanation for the correct answer...">{% if test %}{{ test.explanation }}{% endif %}</textarea>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="is_active" id="is_active" 
                           class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                           {% if not test or test.is_active %}checked{% endif %}>
                    <label for="is_active" class="ml-2 block text-sm text-gray-900">
                        Active (include this question in admission tests)
                    </label>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
            <a href="{% url 'management_admission_tests' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded-lg transition-colors">
                Cancel
            </a>
            <button type="submit" 
                    class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition-transform transform hover:scale-105">
                {% if action == 'Create' %}Create Question{% else %}Update Question{% endif %}
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate correct answer when option is clicked
    const options = document.querySelectorAll('input[name^="option_"]');
    const correctAnswer = document.getElementById('correct_answer');
    
    options.forEach(option => {
        option.addEventListener('blur', function() {
            if (this.value && !correctAnswer.value) {
                // If this is the first option filled and correct answer is empty, suggest it
                if (this.name === 'option_1') {
                    correctAnswer.value = this.value;
                }
            }
        });
        
        option.addEventListener('click', function() {
            if (this.value) {
                correctAnswer.value = this.value;
            }
        });
    });
    
    // Validate that correct answer matches one of the options
    document.querySelector('form').addEventListener('submit', function(e) {
        const correctAnswerValue = correctAnswer.value.trim();
        const optionValues = Array.from(options)
            .map(option => option.value.trim())
            .filter(value => value !== '');
        
        if (correctAnswerValue && !optionValues.includes(correctAnswerValue)) {
            e.preventDefault();
            alert('The correct answer must exactly match one of the provided options.');
            correctAnswer.focus();
        }
        
        // Ensure at least 2 options are provided
        if (optionValues.length < 2) {
            e.preventDefault();
            alert('Please provide at least 2 answer options.');
            document.getElementById('option_1').focus();
        }
    });
});
</script>
{% endblock %}
