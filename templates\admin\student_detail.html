{% extends 'base.html' %}

{% block title %}{{ student.user.get_full_name }} - Student Details - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'admin/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-6">
                <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                    <span class="text-2xl font-bold text-white">
                        {{ student.user.first_name|first }}{{ student.user.last_name|first }}
                    </span>
                </div>
                <div>
                    <div class="flex items-center space-x-3 mb-3">
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 shadow-sm">
                            {{ student.student_id }}
                        </span>
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 shadow-sm">
                            {{ student.get_year_display|default:"No year specified" }}
                        </span>
                        {% if student.major %}
                        <span class="inline-flex px-4 py-2 text-sm font-bold rounded-xl bg-gradient-to-r from-orange-100 to-orange-200 text-orange-800 shadow-sm">
                            {{ student.major.name }}
                        </span>
                        {% endif %}
                    </div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">{{ student.user.get_full_name }}</h1>
                    <p class="text-lg text-gray-600">{{ student.user.email }}</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <a href="{% url 'admin_student_edit' student.id %}" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Student
                </a>
                <a href="{% url 'management_students' %}" class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Students
                </a>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Personal Information -->
        <div class="lg:col-span-1 space-y-6">
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800">Personal Information</h2>
                </div>
                <div class="space-y-6">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Full Name</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.user.get_full_name }}</p>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Email</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.user.email }}</p>
                    </div>
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Student ID</label>
                        <p class="text-lg font-bold text-gray-900 font-mono mt-1">{{ student.student_id }}</p>
                    </div>
                    {% if student.phone_number %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Phone Number</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.phone_number }}</p>
                    </div>
                    {% endif %}
                    {% if student.date_of_birth %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Date of Birth</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.date_of_birth|date:"F j, Y" }}</p>
                    </div>
                    {% endif %}
                    {% if student.address %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Address</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.address }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Academic Summary -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center space-x-3 mb-6">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3z"></path>
                        </svg>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-800">Academic Summary</h2>
                </div>
                <div class="space-y-6">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                        <div class="text-3xl font-bold text-primary-600 mb-1">{{ student.gpa|floatformat:2|default:"N/A" }}</div>
                        <div class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Current GPA</div>
                    </div>
                    {% if student.expected_graduation_year %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Expected Graduation</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.expected_graduation_year }}</p>
                    </div>
                    {% endif %}
                    {% if student.preferred_difficulty %}
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4">
                        <label class="text-sm font-semibold text-gray-600 uppercase tracking-wide">Preferred Difficulty</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">{{ student.get_preferred_difficulty_display }}</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Academic Records and Career Goals -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Career Goals -->
            {% if student.career_goals %}
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Career Goals</h2>
                <p class="text-gray-700 leading-relaxed">{{ student.career_goals }}</p>
            </div>
            {% endif %}

            <!-- Academic Records -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">Academic Records</h2>
                    <span class="text-sm text-gray-500">{{ academic_records.count }} course{{ academic_records.count|pluralize }}</span>
                </div>
                
                {% if academic_records %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Grade</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Credits</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for record in academic_records %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ record.course.code }}</div>
                                        <div class="text-sm text-gray-500">{{ record.course.name }}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {% if record.grade == 'A' or record.grade == 'A+' %}bg-green-100 text-green-800
                                        {% elif record.grade == 'B' or record.grade == 'B+' %}bg-blue-100 text-blue-800
                                        {% elif record.grade == 'C' or record.grade == 'C+' %}bg-yellow-100 text-yellow-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ record.grade|default:"N/A" }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ record.course.credits }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ record.semester|default:"N/A" }} {{ record.year|default:"" }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No academic records</h3>
                    <p class="mt-1 text-sm text-gray-500">This student hasn't enrolled in any courses yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
