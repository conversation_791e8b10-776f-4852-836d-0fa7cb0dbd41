{% extends 'base.html' %}

{% block title %}My Students - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">My Students</h1>
            <p class="text-gray-600">Students assigned to your guidance</p>
        </div>
        <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-500">{{ students_data|length }} student{{ students_data|length|pluralize }}</span>
        </div>
    </div>
</div>

<!-- Students Grid -->
{% if students_data %}
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for item in students_data %}
        <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
            <!-- Student Header -->
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {{ item.student.user.first_name|slice:":1" }}{{ item.student.user.last_name|slice:":1" }}
                </div>
                <div class="flex-1">
                    <h3 class="text-lg font-semibold text-gray-900">{{ item.student.user.get_full_name }}</h3>
                    <p class="text-sm text-gray-600">{{ item.student.major.name|default:"No major selected" }}</p>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Profile Completion</span>
                    <span class="text-sm font-medium text-gray-900">{{ item.completion_percentage|floatformat:0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="h-2 rounded-full {% if item.completion_percentage == 100 %}bg-green-500{% elif item.completion_percentage >= 50 %}bg-yellow-500{% else %}bg-red-500{% endif %}" 
                         style="width: {{ item.completion_percentage }}%"></div>
                </div>
                <div class="text-xs text-gray-500 mt-1">{{ item.completed_count }}/6 components completed</div>
            </div>

            <!-- Component Status -->
            <div class="mb-4">
                <div class="grid grid-cols-3 gap-2">
                    {% for component, completed in item.progress.items %}
                        <div class="flex items-center justify-center p-2 rounded-lg {% if completed %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            <span class="text-xs font-medium">
                                {% if component == 'academic_records' %}📚 Records
                                {% elif component == 'interests' %}🎯 Interests
                                {% elif component == 'career_goals' %}💼 Goals
                                {% elif component == 'major' %}🎓 Major
                                {% elif component == 'admission_test' %}📝 Test
                                {% elif component == 'survey' %}📋 Survey
                                {% endif %}
                            </span>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Student Info -->
            <div class="space-y-2 mb-4">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">GPA:</span>
                    <span class="font-medium text-gray-900">{{ item.student.gpa|default:"N/A" }}</span>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Year:</span>
                    <span class="font-medium text-gray-900">{{ item.student.get_year_display }}</span>
                </div>
                {% if item.latest_recommendation %}
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Latest Rec:</span>
                    <span class="font-medium text-gray-900">{{ item.latest_recommendation.course.code }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-2">
                <a href="{% url 'counselor_student_detail' item.student.id %}" 
                   class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors">
                    View Details
                </a>
                <a href="{% url 'counselor_feedback_create' item.student.id %}" 
                   class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Send Feedback
                </a>
            </div>

            <!-- Priority Indicator -->
            {% if item.completion_percentage < 50 %}
                <div class="mt-3 flex items-center justify-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        Needs Attention
                    </span>
                </div>
            {% elif item.completion_percentage == 100 %}
                <div class="mt-3 flex items-center justify-center">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Profile Complete
                    </span>
                </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No students assigned</h3>
        <p class="mt-1 text-sm text-gray-500">You don't have any students assigned to your guidance yet.</p>
    </div>
{% endif %}
{% endblock %}
