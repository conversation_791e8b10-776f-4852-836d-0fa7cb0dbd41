{% extends 'auth_base.html' %}

{% block title %}Student Registration - CourseRec{% endblock %}

{% block extra_css %}
<style>
    .registration-container {
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .step-animation {
        animation: slideInRight 0.5s ease-out;
    }

    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .progress-bar-fill {
        transition: width 0.5s ease-in-out;
    }

    .form-field-valid {
        border-color: #10b981;
        background-color: #f0fdf4;
    }

    .form-field-invalid {
        border-color: #ef4444;
        background-color: #fef2f2;
    }

    .floating-label {
        transition: all 0.3s ease;
    }

    .form-input:focus + .floating-label,
    .form-input:not(:placeholder-shown) + .floating-label {
        transform: translateY(-1.5rem) scale(0.85);
        color: #2563eb;
    }

    .btn-loading {
        position: relative;
        color: transparent;
    }

    .btn-loading::after {
        content: "";
        position: absolute;
        width: 16px;
        height: 16px;
        top: 50%;
        left: 50%;
        margin-left: -8px;
        margin-top: -8px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }

    .step-indicator {
        transition: all 0.3s ease;
    }

    .step-indicator.active {
        background: linear-gradient(135deg, #2563eb, #7c3aed);
        transform: scale(1.1);
    }

    .step-indicator.completed {
        background: linear-gradient(135deg, #10b981, #059669);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen lg:grid lg:grid-cols-12 bg-gradient-to-br from-blue-50 via-white to-purple-50">
    <div class="hidden lg:block lg:col-span-5 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80');">
        <div class="h-full bg-primary-800 bg-opacity-60 flex flex-col justify-between p-12">
            <div>
                <h2 class="text-3xl font-bold text-white">CourseRec</h2>
                <p class="text-primary-200 mt-2">Your personalized guide to academic success.</p>
            </div>
            <div>
                <p class="text-white text-lg font-medium">“The beautiful thing about learning is that no one can take it away from you.”</p>
                <p class="text-primary-300 mt-2">— B.B. King</p>
            </div>
        </div>
    </div>

    <div class="lg:col-span-7 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-lg w-full space-y-8">
            <!-- Header Section -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-gradient-to-r from-primary-600 to-purple-600 rounded-full flex items-center justify-center mb-4">
                    <svg class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                </div>
                <h2 class="text-3xl font-extrabold text-gray-900 mb-2">
                    Join CourseRec
                </h2>
                <p class="text-sm text-gray-600 mb-4">
                    Create your account to get personalized course recommendations
                </p>
                <p class="text-center text-sm text-gray-600">
                    Already have an account?
                    <a href="{% url 'login' %}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                        Sign in here
                    </a>
                </p>
            </div>
            <!-- Enhanced Registration Form -->
            <div class="registration-container rounded-2xl p-8 shadow-xl">
                <form class="space-y-6" method="POST" id="registration-form">
                    {% csrf_token %}

                    <!-- Enhanced Progress Bar -->
                    <div class="mb-8">
                        <div class="flex items-center justify-between mb-4">
                            <span class="text-sm font-medium text-gray-600">Step <span id="current-step-number">1</span> of 3</span>
                            <span class="text-sm font-medium text-gray-600"><span id="progress-percentage">33</span>% Complete</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-primary-600 to-purple-600 h-2 rounded-full progress-bar-fill" style="width: 33%" id="progress-bar"></div>
                        </div>

                        <!-- Step Indicators -->
                        <div class="flex items-center justify-between mt-6">
                            <!-- Step 1 -->
                            <div class="flex flex-col items-center">
                                <div class="step-indicator active w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-medium" id="step-indicator-1">
                                    1
                                </div>
                                <span class="text-xs font-medium text-primary-600 mt-2" id="step-label-1">Account</span>
                            </div>
                            <!-- Step 2 -->
                            <div class="flex flex-col items-center">
                                <div class="step-indicator w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center text-gray-500 text-sm font-medium" id="step-indicator-2">
                                    2
                                </div>
                                <span class="text-xs font-medium text-gray-500 mt-2" id="step-label-2">Personal</span>
                            </div>
                            <!-- Step 3 -->
                            <div class="flex flex-col items-center">
                                <div class="step-indicator w-10 h-10 rounded-full border-2 border-gray-300 flex items-center justify-center text-gray-500 text-sm font-medium" id="step-indicator-3">
                                    3
                                </div>
                                <span class="text-xs font-medium text-gray-500 mt-2" id="step-label-3">Academic</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 1: Account Information -->
                    <div id="step-1" class="step step-animation">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Account Information</h3>
                            <p class="text-sm text-gray-600">Create your login credentials</p>
                        </div>
                        <div class="space-y-5">
                            {% for field in form %}{% if field.name in "username,email,password,confirm_password" %}
                            <div class="relative">
                                <div class="relative">
                                    {{ field }}
                                    {% if field.name == 'password' or field.name == 'confirm_password' %}
                                    <button type="button"
                                            class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                                            onclick="togglePasswordField('{{ field.id_for_label }}')">
                                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                        </svg>
                                    </button>
                                    {% endif %}
                                </div>
                                <label for="{{ field.id_for_label }}" class="absolute left-3 -top-2 bg-white px-1 text-xs font-medium text-gray-700">
                                    {{ field.label }}
                                </label>
                                {% if field.errors %}
                                    <div class="text-red-600 text-xs mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        {% for error in field.errors %}<span>{{ error }}</span>{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="field-validation-icon absolute right-3 top-3 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            {% endif %}{% endfor %}
                        </div>
                    </div>

                    <!-- Step 2: Personal Information -->
                    <div id="step-2" class="step hidden step-animation">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Personal Information</h3>
                            <p class="text-sm text-gray-600">Tell us about yourself</p>
                        </div>
                        <div class="space-y-5">
                            {% for field in form %}{% if field.name in "first_name,last_name,date_of_birth,phone_number,address" %}
                            <div class="relative">
                                {{ field }}
                                <label for="{{ field.id_for_label }}" class="absolute left-3 -top-2 bg-white px-1 text-xs font-medium text-gray-700">
                                    {{ field.label }}
                                </label>
                                {% if field.errors %}
                                    <div class="text-red-600 text-xs mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        {% for error in field.errors %}<span>{{ error }}</span>{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="field-validation-icon absolute right-3 top-3 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            {% endif %}{% endfor %}
                        </div>
                    </div>

                    <!-- Step 3: Academic Profile -->
                    <div id="step-3" class="step hidden step-animation">
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Academic Profile</h3>
                            <p class="text-sm text-gray-600">Help us personalize your experience</p>
                        </div>
                        <div class="space-y-5">
                            {% for field in form %}{% if field.name in "student_id,year,major,expected_graduation_year,career_goals,preferred_difficulty" %}
                            <div class="relative">
                                {{ field }}
                                <label for="{{ field.id_for_label }}" class="absolute left-3 -top-2 bg-white px-1 text-xs font-medium text-gray-700">
                                    {{ field.label }}
                                </label>
                                {% if field.errors %}
                                    <div class="text-red-600 text-xs mt-1 flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        {% for error in field.errors %}<span>{{ error }}</span>{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="field-validation-icon absolute right-3 top-3 hidden">
                                    <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            {% endif %}{% endfor %}
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="pt-6 flex justify-between items-center">
                        <button type="button"
                                id="prev-btn"
                                class="group relative flex justify-center py-3 px-6 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 transform hover:scale-105 shadow-sm hidden">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                        </button>

                        <button type="button"
                                id="next-btn"
                                class="group relative flex justify-center py-3 px-6 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-primary-600 to-purple-600 hover:from-primary-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                            <span id="next-btn-text">Next</span>
                            <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>

                        <button type="submit"
                                id="submit-btn"
                                class="group relative w-full flex justify-center py-3 px-6 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl hidden">
                            <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span id="submit-btn-text">Create Account</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Enhanced Right Side Panel -->
    <div class="hidden lg:block lg:col-span-5 relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-600 via-blue-600 to-purple-700"></div>
        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        <div class="absolute inset-0" style="background-image: url('https://images.unsplash.com/photo-*************-9a054b0db644?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'); background-size: cover; background-position: center; opacity: 0.3;"></div>

        <div class="relative h-full flex flex-col justify-between p-12 text-white">
            <div class="space-y-8">
                <div class="space-y-4">
                    <h2 class="text-4xl font-bold">Start Your Academic Journey</h2>
                    <p class="text-xl text-blue-100">Join thousands of students discovering their perfect courses</p>
                </div>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Personalized Recommendations</h3>
                            <p class="text-blue-100">Get course suggestions tailored to your academic goals and interests</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd" />
                                <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Career-Focused Learning</h3>
                            <p class="text-blue-100">Align your course selection with your future career aspirations</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 h-12 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">Academic Progress Tracking</h3>
                            <p class="text-blue-100">Monitor your academic journey and stay on track for graduation</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <blockquote class="text-xl font-medium italic">
                    "Education is the most powerful weapon which you can use to change the world."
                </blockquote>
                <cite class="text-blue-200 text-sm">— Nelson Mandela</cite>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function togglePasswordField(fieldId) {
        const field = document.getElementById(fieldId);
        const button = field.nextElementSibling;
        const icon = button.querySelector('svg');

        if (field.type === 'password') {
            field.type = 'text';
            icon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            `;
        } else {
            field.type = 'password';
            icon.innerHTML = `
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            `;
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const steps = Array.from(document.querySelectorAll('.step'));
        const nextBtn = document.getElementById('next-btn');
        const prevBtn = document.getElementById('prev-btn');
        const submitBtn = document.getElementById('submit-btn');
        const progressBar = document.getElementById('progress-bar');
        const currentStepNumber = document.getElementById('current-step-number');
        const progressPercentage = document.getElementById('progress-percentage');

        let currentStep = 0;
        const totalSteps = steps.length;

        function updateProgressBar(stepIndex) {
            progressBarElements.icons.forEach((icon, index) => {
                const text = progressBarElements.texts[index];
                if (index === stepIndex) {
                    icon.classList.add('border-primary-600'); icon.classList.remove('border-gray-300');
                    text.classList.add('text-primary-600'); text.classList.remove('text-gray-500');
                } else if (index < stepIndex) {
                    icon.classList.add('border-primary-600'); icon.classList.remove('border-gray-300');
                    text.classList.add('text-primary-600'); text.classList.remove('text-gray-500');
                } else {
                    icon.classList.add('border-gray-300'); icon.classList.remove('border-primary-600');
                    text.classList.add('text-gray-500'); text.classList.remove('text-primary-600');
                }
            });
            progressBarElements.connectors.forEach((connector, index) => {
                if (index < stepIndex) {
                    connector.classList.add('border-primary-600'); connector.classList.remove('border-gray-300');
                } else {
                    connector.classList.add('border-gray-300'); connector.classList.remove('border-primary-600');
                }
            });
        }

        function showStep(stepIndex) {
            steps.forEach((step, index) => step.classList.toggle('hidden', index !== stepIndex));
            prevBtn.classList.toggle('hidden', stepIndex === 0);
            nextBtn.classList.toggle('hidden', stepIndex === steps.length - 1);
            submitBtn.classList.toggle('hidden', stepIndex !== steps.length - 1);

            if (stepIndex === 0) {
                prevBtn.classList.add('hidden');
                nextBtn.style.width = '100%';
            } else {
                nextBtn.style.width = 'auto';
                prevBtn.style.flexGrow = '0';
                nextBtn.style.flexGrow = '1';
            }
            if (stepIndex === steps.length - 1) {
                prevBtn.style.flexGrow = '0';
                submitBtn.style.flexGrow = '1';
            }

            updateProgressBar(stepIndex);
        }

        nextBtn.addEventListener('click', () => {
            if (currentStep < steps.length - 1) showStep(++currentStep);
        });

        prevBtn.addEventListener('click', () => {
            if (currentStep > 0) showStep(--currentStep);
        });

        // Enhanced form validation
        function validateCurrentStep() {
            const currentStepElement = steps[currentStep];
            const inputs = currentStepElement.querySelectorAll('input, select, textarea');
            let isValid = true;

            inputs.forEach(input => {
                const value = input.value.trim();
                const isRequired = input.hasAttribute('required') || input.classList.contains('required');

                if (isRequired && !value) {
                    input.classList.add('form-field-invalid');
                    input.classList.remove('form-field-valid');
                    isValid = false;
                } else if (value) {
                    // Additional validation based on field type
                    let fieldValid = true;

                    if (input.type === 'email') {
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        fieldValid = emailRegex.test(value);
                    } else if (input.name === 'confirm_password') {
                        const passwordField = document.querySelector('input[name="password"]');
                        fieldValid = passwordField && passwordField.value === value;
                    }

                    if (fieldValid) {
                        input.classList.add('form-field-valid');
                        input.classList.remove('form-field-invalid');
                        const icon = input.parentElement.querySelector('.field-validation-icon');
                        if (icon) icon.classList.remove('hidden');
                    } else {
                        input.classList.add('form-field-invalid');
                        input.classList.remove('form-field-valid');
                        isValid = false;
                    }
                }
            });

            return isValid;
        }

        // Real-time validation
        steps.forEach(step => {
            const inputs = step.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    validateCurrentStep();
                });

                input.addEventListener('input', () => {
                    if (input.classList.contains('form-field-invalid')) {
                        validateCurrentStep();
                    }
                });
            });
        });

        // Form submission with loading state
        document.getElementById('registration-form').addEventListener('submit', function(e) {
            if (!validateCurrentStep()) {
                e.preventDefault();
                return;
            }

            const submitBtn = document.getElementById('submit-btn');
            const btnText = document.getElementById('submit-btn-text');

            submitBtn.classList.add('btn-loading');
            submitBtn.disabled = true;
            btnText.textContent = 'Creating Account...';

            // Re-enable button after 10 seconds in case of error
            setTimeout(() => {
                submitBtn.classList.remove('btn-loading');
                submitBtn.disabled = false;
                btnText.textContent = 'Create Account';
            }, 10000);
        });

        showStep(currentStep);
    });
</script>
{% endblock %}
