{% extends 'base.html' %}

{% block title %}Student Progress Tracking - {{ block.super }}{% endblock %}

{% block sidebar %}
    {% include 'counselor/partials/sidebar.html' %}
{% endblock %}

{% block content %}
<!-- Header -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6 mb-8">
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Student Progress Tracking</h1>
            <p class="text-gray-600">Monitor completion status of all profile components</p>
        </div>
        <div class="flex items-center space-x-4">
            <!-- Filter Dropdown -->
            <div class="relative">
                <select id="statusFilter" class="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    <option value="all" {% if filter_status == 'all' %}selected{% endif %}>All Students</option>
                    <option value="incomplete" {% if filter_status == 'incomplete' %}selected{% endif %}>Incomplete Profiles</option>
                    <option value="complete" {% if filter_status == 'complete' %}selected{% endif %}>Complete Profiles</option>
                    <option value="needs_attention" {% if filter_status == 'needs_attention' %}selected{% endif %}>Needs Attention</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                    <svg class="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Progress Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 rounded-full mr-4">
                <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H2z"/>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Total Students</h3>
                <p class="text-2xl font-bold text-blue-600">{{ total_students }}</p>
            </div>
        </div>
    </div>
    
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 rounded-full mr-4">
                <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Complete Profiles</h3>
                <p class="text-2xl font-bold text-green-600">
                    {% with complete_count=progress_data|length %}
                        {% for item in progress_data %}
                            {% if item.completion_percentage == 100 %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>
    
    <div class="bg-white/80 backdrop-blur-sm p-6 rounded-xl shadow-lg border border-white/20">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 rounded-full mr-4">
                <svg class="w-6 h-6 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Need Attention</h3>
                <p class="text-2xl font-bold text-red-600">
                    {% with attention_count=0 %}
                        {% for item in progress_data %}
                            {% if item.completion_percentage < 50 %}{{ forloop.counter0|add:1 }}{% endif %}
                        {% endfor %}
                    {% endwith %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Student Progress Table -->
<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-xl font-semibold text-gray-800">Student Progress Details</h2>
    </div>
    
    {% if progress_data %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50/50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overall Progress</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Components</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GPA</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Latest Test</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white/50 divide-y divide-gray-200">
                    {% for item in progress_data %}
                    <tr class="hover:bg-gray-50/50 transition-colors">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                                    {{ item.student.user.first_name|slice:":1" }}{{ item.student.user.last_name|slice:":1" }}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ item.student.user.get_full_name }}</div>
                                    <div class="text-sm text-gray-500">{{ item.student.major.name|default:"No major" }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-16 bg-gray-200 rounded-full h-2 mr-3">
                                    <div class="h-2 rounded-full {% if item.completion_percentage == 100 %}bg-green-500{% elif item.completion_percentage >= 50 %}bg-yellow-500{% else %}bg-red-500{% endif %}" 
                                         style="width: {{ item.completion_percentage }}%"></div>
                                </div>
                                <span class="text-sm font-medium text-gray-900">{{ item.completion_percentage|floatformat:0 }}%</span>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">{{ item.completed_count }}/6 components</div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="flex flex-wrap gap-1">
                                {% for component, details in item.component_details.items %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if details.completed %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                        {% if component == 'academic_records' %}📚
                                        {% elif component == 'interests' %}🎯
                                        {% elif component == 'career_goals' %}🎯
                                        {% elif component == 'major' %}🎓
                                        {% elif component == 'admission_test' %}📝
                                        {% elif component == 'survey' %}📋
                                        {% endif %}
                                    </span>
                                {% endfor %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="text-sm font-medium text-gray-900">{{ item.gpa|default:"N/A" }}</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if item.latest_test %}
                                <span class="text-sm font-medium text-gray-900">{{ item.latest_test.percentage_score|floatformat:1 }}%</span>
                                <div class="text-xs text-gray-500">{{ item.latest_test.completed_at|date:"M d" }}</div>
                            {% else %}
                                <span class="text-sm text-gray-500">Not taken</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{% url 'counselor_student_detail' item.student.id %}" 
                               class="text-purple-600 hover:text-purple-900 mr-3">View Details</a>
                            <a href="{% url 'counselor_feedback_create' item.student.id %}" 
                               class="text-blue-600 hover:text-blue-900">Send Feedback</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
            <p class="mt-1 text-sm text-gray-500">No students match the current filter criteria.</p>
        </div>
    {% endif %}
</div>

<script>
document.getElementById('statusFilter').addEventListener('change', function() {
    const status = this.value;
    const url = new URL(window.location);
    if (status === 'all') {
        url.searchParams.delete('status');
    } else {
        url.searchParams.set('status', status);
    }
    window.location.href = url.toString();
});
</script>
{% endblock %}
